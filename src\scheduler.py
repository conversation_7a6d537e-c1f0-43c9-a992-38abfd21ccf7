#!/usr/bin/env python3
# coding: utf-8
"""
定时任务调度器
实现1h任务（整点+4分钟）和6m任务（0,6,12...分钟+4分钟）的调度逻辑
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Callable, Awaitable, Optional
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent
sys.path.insert(0, str(src_path))

from task_manager import task_manager, TaskInfo
from uptime_checker import check_and_get_fresh_data
from resource_manager import resource_manager
from weather_download import download_weather_data_with_api_result
from task_state_manager import task_state_manager, mark_task_started, mark_task_completed
from config import (
    SCHEDULER_HOURLY_DATA_TYPES,
    SCHEDULER_SIX_MINUTE_DATA_TYPE, SCHEDULER_HOURLY_TIMEOUT_MINUTES,
    SCHEDULER_SIX_MINUTE_TIMEOUT_MINUTES, WEATHER_ALARM_CONFIG,
    SCHEDULER_HOURLY_SCHEDULE_PATTERN, SCHEDULER_SIX_MINUTE_SCHEDULE_PATTERN,
    SCHEDULER_OBSERVATION_DATA_TYPE, SCHEDULER_OBSERVATION_SCHEDULE_PATTERN
)

logger = logging.getLogger(__name__)


class CronScheduleParser:
    """简单的 cron 表达式解析器，支持分钟级调度"""

    @staticmethod
    def parse_minute_pattern(pattern: str) -> List[int]:
        """
        解析分钟模式，支持：
        - 单个数字: "4" -> [4]
        - 逗号分隔: "4,10,16" -> [4, 10, 16]
        - 范围: "0-5" -> [0, 1, 2, 3, 4, 5]
        - 步长: "*/10" -> [0, 10, 20, 30, 40, 50]
        - 混合: "4,10,16,22,28,34,40,46,52,58" -> [4, 10, 16, 22, 28, 34, 40, 46, 52, 58]
        """
        if not pattern or pattern == "*":
            return list(range(60))  # 每分钟

        minutes = []
        parts = pattern.split(',')

        for part in parts:
            part = part.strip()

            if '/' in part:
                # 步长模式: */10 或 0-30/5
                if part.startswith('*/'):
                    step = int(part[2:])
                    minutes.extend(range(0, 60, step))
                else:
                    range_part, step = part.split('/')
                    if '-' in range_part:
                        start, end = map(int, range_part.split('-'))
                    else:
                        start, end = 0, 59
                    step = int(step)
                    minutes.extend(range(start, end + 1, step))
            elif '-' in part:
                # 范围模式: 0-5
                start, end = map(int, part.split('-'))
                minutes.extend(range(start, end + 1))
            else:
                # 单个数字: 4
                minutes.append(int(part))

        # 去重并排序
        return sorted(list(set(m for m in minutes if 0 <= m <= 59)))

    @staticmethod
    def parse_cron_pattern(pattern: str) -> Dict[str, List[int]]:
        """
        解析简化的 cron 表达式，格式: "分钟 小时 日 月 周"
        目前只支持分钟和小时字段
        """
        parts = pattern.split()
        if len(parts) < 2:
            raise ValueError(f"无效的 cron 表达式: {pattern}")

        minute_pattern = parts[0]
        hour_pattern = parts[1]

        minutes = CronScheduleParser.parse_minute_pattern(minute_pattern)

        # 解析小时模式
        if hour_pattern == "*":
            hours = list(range(24))  # 每小时
        else:
            hours = CronScheduleParser.parse_minute_pattern(hour_pattern)  # 复用分钟解析逻辑
            hours = [h for h in hours if 0 <= h <= 23]  # 限制在0-23范围内

        return {
            'minutes': minutes,
            'hours': hours
        }


class WeatherTaskScheduler:
    """天气数据任务调度器"""
    
    def __init__(self):
        self.task_manager = task_manager
        self.resource_manager = resource_manager
        self._shutdown = False
        self._scheduler_tasks = []

        # 1小时任务的数据类型
        self.hourly_data_types = SCHEDULER_HOURLY_DATA_TYPES

        # 6分钟任务的数据类型
        self.six_minute_data_type = SCHEDULER_SIX_MINUTE_DATA_TYPE

        # 实况数据任务的数据类型
        self.observation_data_type = SCHEDULER_OBSERVATION_DATA_TYPE

        # 预警任务配置
        self.alarm_interval_minutes = WEATHER_ALARM_CONFIG.get("schedule_interval_minutes", 10)

        # 调度模式配置
        self.hourly_schedule_pattern = SCHEDULER_HOURLY_SCHEDULE_PATTERN
        self.six_minute_schedule_pattern = SCHEDULER_SIX_MINUTE_SCHEDULE_PATTERN
        self.observation_schedule_pattern = SCHEDULER_OBSERVATION_SCHEDULE_PATTERN

        # 解析调度模式
        try:
            self.hourly_schedule = CronScheduleParser.parse_cron_pattern(self.hourly_schedule_pattern)
            logger.info(f"1小时任务调度模式: {self.hourly_schedule_pattern} -> 分钟: {self.hourly_schedule['minutes']}, 小时: {self.hourly_schedule['hours']}")
        except Exception as e:
            logger.error(f"解析1小时任务调度模式失败: {e}, 使用默认模式")
            self.hourly_schedule = {'minutes': [4], 'hours': list(range(24))}

        try:
            self.six_minute_schedule = CronScheduleParser.parse_cron_pattern(self.six_minute_schedule_pattern)
            logger.info(f"6分钟任务调度模式: {self.six_minute_schedule_pattern} -> 分钟: {self.six_minute_schedule['minutes']}")
        except Exception as e:
            logger.error(f"解析6分钟任务调度模式失败: {e}, 使用默认模式")
            self.six_minute_schedule = {'minutes': [4, 10, 16, 22, 28, 34, 40, 46, 52, 58], 'hours': list(range(24))}

        try:
            self.observation_schedule = CronScheduleParser.parse_cron_pattern(self.observation_schedule_pattern)
            logger.info(f"实况数据任务调度模式: {self.observation_schedule_pattern} -> 分钟: {self.observation_schedule['minutes']}")
        except Exception as e:
            logger.error(f"解析实况数据任务调度模式失败: {e}, 使用默认模式")
            self.observation_schedule = {'minutes': [8, 18, 28, 38, 48, 58], 'hours': list(range(24))}

        # 初始化标志
        self._forecast_tables_created = False
    
    def _get_next_hourly_schedule(self, current_time: datetime = None) -> datetime:
        """获取下一个1小时任务的调度时间（基于配置的调度模式）"""
        if current_time is None:
            current_time = datetime.now()

        schedule_minutes = self.hourly_schedule['minutes']
        schedule_hours = self.hourly_schedule['hours']

        # 如果当前小时在调度小时列表中，检查是否有未来的分钟
        current_hour = current_time.hour
        current_minute = current_time.minute

        if current_hour in schedule_hours:
            # 找到当前小时内下一个调度分钟
            for minute in schedule_minutes:
                schedule_time = current_time.replace(
                    minute=minute,
                    second=0,
                    microsecond=0
                )
                if schedule_time > current_time:
                    return schedule_time

        # 如果当前小时内没有找到，或当前小时不在调度列表中，找下一个调度小时
        for hour_offset in range(1, 25):  # 最多查找24小时
            next_hour = (current_hour + hour_offset) % 24
            if next_hour in schedule_hours:
                # 使用该小时的第一个调度分钟
                first_minute = min(schedule_minutes)
                next_schedule = current_time.replace(
                    hour=next_hour,
                    minute=first_minute,
                    second=0,
                    microsecond=0
                )

                # 如果跨天了，需要加一天
                if next_hour < current_hour:
                    next_schedule += timedelta(days=1)

                return next_schedule

        # 如果没有找到（理论上不应该发生），返回下一个小时的第一个分钟
        first_minute = min(schedule_minutes)
        return current_time.replace(
            minute=first_minute,
            second=0,
            microsecond=0
        ) + timedelta(hours=1)
    
    def _get_next_six_minute_schedule(self, current_time: datetime = None) -> datetime:
        """获取下一个6分钟任务的调度时间（基于配置的调度模式）"""
        if current_time is None:
            current_time = datetime.now()

        schedule_minutes = self.six_minute_schedule['minutes']
        schedule_hours = self.six_minute_schedule['hours']

        current_hour = current_time.hour
        current_minute = current_time.minute

        # 如果当前小时在调度小时列表中，检查是否有未来的分钟
        if current_hour in schedule_hours:
            # 找到当前小时内下一个调度分钟
            for minute in schedule_minutes:
                schedule_time = current_time.replace(
                    minute=minute,
                    second=0,
                    microsecond=0
                )
                if schedule_time > current_time:
                    return schedule_time

        # 如果当前小时内没有找到，或当前小时不在调度列表中，找下一个调度小时
        for hour_offset in range(1, 25):  # 最多查找24小时
            next_hour = (current_hour + hour_offset) % 24
            if next_hour in schedule_hours:
                # 使用该小时的第一个调度分钟
                first_minute = min(schedule_minutes)
                next_schedule = current_time.replace(
                    hour=next_hour,
                    minute=first_minute,
                    second=0,
                    microsecond=0
                )

                # 如果跨天了，需要加一天
                if next_hour < current_hour:
                    next_schedule += timedelta(days=1)

                return next_schedule

        # 如果没有找到（理论上不应该发生），返回下一个小时的第一个分钟
        first_minute = min(schedule_minutes)
        return current_time.replace(
            minute=first_minute,
            second=0,
            microsecond=0
        ) + timedelta(hours=1)

    def _get_next_observation_schedule(self, current_time: datetime = None) -> datetime:
        """获取下一个实况数据任务的调度时间（基于配置的调度模式）"""
        if current_time is None:
            current_time = datetime.now()

        schedule_minutes = self.observation_schedule['minutes']
        current_minute = current_time.minute

        # 找到下一个调度分钟
        next_minute = None
        for minute in schedule_minutes:
            if minute > current_minute:
                next_minute = minute
                break

        if next_minute is not None:
            # 在当前小时内找到下一个调度时间
            return current_time.replace(
                minute=next_minute,
                second=0,
                microsecond=0
            )
        else:
            # 需要到下一个小时的第一个调度时间
            first_minute = min(schedule_minutes)
            return current_time.replace(
                minute=first_minute,
                second=0,
                microsecond=0
            ) + timedelta(hours=1)

    def _get_next_alarm_schedule(self, current_time: datetime = None) -> datetime:
        """获取下一个预警任务的调度时间（每10分钟执行一次）"""
        if current_time is None:
            current_time = datetime.now()

        # 计算下一个10分钟的整点时间
        minutes = current_time.minute
        next_minute = ((minutes // self.alarm_interval_minutes) + 1) * self.alarm_interval_minutes

        if next_minute >= 60:
            # 如果超过60分钟，调度到下一个小时
            next_schedule = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        else:
            next_schedule = current_time.replace(minute=next_minute, second=0, microsecond=0)

        return next_schedule

    def _format_uptime(self, uptime: str) -> Optional[str]:
        """格式化uptime为YYYYMMDDHHMM格式"""
        if not uptime:
            return None

        # 移除所有非数字字符
        clean_uptime = ''.join(filter(str.isdigit, uptime))

        # 根据长度判断格式
        if len(clean_uptime) == 12:  # YYYYMMDDHHMM
            # 验证年份和月份是否合理
            year = clean_uptime[:4]
            month = clean_uptime[4:6]
            day = clean_uptime[6:8]

            # 检查是否是异常格式（如 "202025071308"）
            try:
                month_int = int(month)
                if clean_uptime.startswith('2020') and month_int > 12:
                    # 可能是年份重复的异常格式，尝试修正
                    # "202025071308" -> "202507130800" (去掉重复的"20")
                    corrected = '20' + clean_uptime[4:]
                    if len(corrected) >= 10:  # 至少要有10位数字
                        # 如果长度不够12位，可能需要补充
                        if len(corrected) == 10:
                            corrected = corrected + "00"  # 补充分钟数
                        elif len(corrected) > 12:
                            corrected = corrected[:12]  # 截取前12位

                        if len(corrected) == 12:
                            # 验证修正后的格式
                            corrected_month = corrected[4:6]
                            corrected_day = corrected[6:8]
                            try:
                                corrected_month_int = int(corrected_month)
                                corrected_day_int = int(corrected_day)
                                if 1 <= corrected_month_int <= 12 and 1 <= corrected_day_int <= 31:
                                    logger.info(f"修正异常uptime格式: {uptime} -> {corrected}")
                                    return corrected
                            except ValueError:
                                pass
            except ValueError:
                pass

            # 正常验证年份范围
            if '2020' <= year <= '2030' and '01' <= month <= '12' and '01' <= day <= '31':
                return clean_uptime
            else:
                logger.warning(f"日期不合理: 年={year}, 月={month}, 日={day}, 原始uptime: {uptime}")
                return None
        elif len(clean_uptime) == 14:  # YYYYMMDDHHMMSS，去掉秒
            year = clean_uptime[:4]
            if year.startswith('20') and '20' <= year <= '30':
                return clean_uptime[:12]
            else:
                logger.warning(f"年份不合理: {year}, 原始uptime: {uptime}")
                return None
        elif len(clean_uptime) == 10:  # YYMMDDHHMM，补充20前缀
            return "20" + clean_uptime
        elif len(clean_uptime) > 12:
            # 处理异常长度的情况，可能是年份重复了
            # 例如 "202025071308" -> "202507131308" (去掉重复的"20")
            if clean_uptime.startswith('2020') and len(clean_uptime) >= 14:
                # 去掉重复的"20"
                corrected = '20' + clean_uptime[4:]
                if len(corrected) >= 12:
                    result = corrected[:12]
                    logger.info(f"修正异常uptime格式: {uptime} -> {result}")
                    return result
            logger.warning(f"无法识别的uptime格式: {uptime} (长度: {len(clean_uptime)})")
            return None
        else:
            logger.warning(f"无法识别的uptime格式: {uptime} (长度: {len(clean_uptime)})")
            return None

    async def _interruptible_sleep(self, seconds: float):
        """可中断的睡眠，每秒检查一次关闭标志"""
        remaining = seconds
        while remaining > 0 and not self._shutdown:
            sleep_time = min(1.0, remaining)  # 每次最多睡眠1秒
            await asyncio.sleep(sleep_time)
            remaining -= sleep_time

    def _create_forecast_tables(self):
        """同步执行create_forecast_tables.py创建预报表"""
        if self._forecast_tables_created:
            return

        logger.info("开始执行create_forecast_tables.py创建预报表...")

        try:
            # 直接同步调用函数，确保表和存储过程创建完成
            self._run_create_forecast_tables()

            self._forecast_tables_created = True
            logger.info("预报表创建完成")

        except Exception as e:
            logger.error(f"创建预报表失败: {e}")
            # 预报表创建失败是致命错误，必须停止启动
            raise RuntimeError(f"预报表创建失败，系统无法启动: {e}")

    def _run_create_forecast_tables(self):
        """在线程中运行create_forecast_tables"""
        try:
            logger.info("开始执行create_forecast_tables...")

            # 直接导入并调用函数，避免subprocess的问题
            from create_forecast_tables import create_all_tables

            # 重定向输出到日志
            import io
            import sys
            from contextlib import redirect_stdout, redirect_stderr

            stdout_buffer = io.StringIO()
            stderr_buffer = io.StringIO()

            with redirect_stdout(stdout_buffer), redirect_stderr(stderr_buffer):
                create_all_tables()

            # 获取输出
            stdout_content = stdout_buffer.getvalue()
            stderr_content = stderr_buffer.getvalue()

            if stdout_content:
                logger.info(f"create_forecast_tables输出: {stdout_content}")

            if stderr_content:
                logger.warning(f"create_forecast_tables错误: {stderr_content}")

            logger.info("create_forecast_tables执行成功")

        except Exception as e:
            logger.error(f"执行create_forecast_tables失败: {e}")
            raise
    
    async def _execute_hourly_task(self, data_type: str) -> Any:
        """执行1小时任务"""
        logger.info(f"开始执行1小时任务: {data_type}")

        download_success = False
        formatted_uptime = None

        try:
            # 1. 获取当前任务状态，用于检查uptime新鲜度
            from task_state_manager import task_state_manager
            task_state = task_state_manager.get_task_state("1h", data_type)
            last_processed_uptime = task_state.last_uptime if task_state else None

            # 2. 检查数据新鲜度并获取下载信息（会自动重试直到获取到新数据）
            download_type = f"gz_didiforecast1h{data_type}"
            api_result = await check_and_get_fresh_data(download_type, "1h", last_processed_uptime)

            if not api_result:
                raise Exception(f"无法获取新鲜的 {data_type} 数据")

            # 3. 获取uptime信息
            current_uptime = api_result.get('uptime', '')
            if not current_uptime:
                raise Exception(f"API响应中未找到uptime信息")

            # 格式化uptime（确保是YYYYMMDDHHMM格式）
            formatted_uptime = self._format_uptime(current_uptime)
            if not formatted_uptime:
                raise Exception(f"无效的uptime格式: {current_uptime}")

            logger.info(f"获取到新数据，uptime: {formatted_uptime}，开始下载...")

            # 3. 立即标记任务开始处理（更新uptime状态）
            mark_task_started("1h", data_type, formatted_uptime)

            # 4. 异步下载数据（使用线程池避免阻塞）
            loop = asyncio.get_event_loop()
            downloaded_files = await loop.run_in_executor(
                self.resource_manager.get_thread_pool(),
                download_weather_data_with_api_result,
                download_type, api_result
            )
            if not downloaded_files:
                raise Exception(f"下载 {data_type} 数据失败")

            logger.info(f"数据下载完成: {downloaded_files}")
            download_success = True

            # 5. 执行数据处理
            logger.info(f"开始处理 {data_type} 数据...")

            # 调用实际的数据处理逻辑（使用线程池异步执行）
            try:
                from weather_cell_1h import process_weather_data_hybrid

                # 为1小时任务构建文件字典 - 现在使用所有下载的文件
                nc_files_dict = {data_type: downloaded_files} if downloaded_files else None

                # 使用线程池异步执行数据处理，避免阻塞其他任务
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    self.resource_manager.get_thread_pool(),
                    lambda: asyncio.run(process_weather_data_hybrid([data_type], nc_files_dict))
                )
                logger.info(f"1小时数据 {data_type} 处理完成")

            except Exception as e:
                logger.error(f"1小时数据 {data_type} 处理失败: {e}")
                # 不抛出异常，让任务标记为完成但记录错误

        except Exception as e:
            logger.error(f"1小时任务 {data_type} 下载失败: {e}")
            download_success = False

        # 6. 无论下载是否成功，都要执行最终步骤
        try:
            await self._execute_hourly_final_steps(data_type, download_success, formatted_uptime)
        except Exception as e:
            logger.error(f"1小时任务 {data_type} 最终步骤执行失败: {e}")

        # 7. 标记任务完成（如果有uptime且下载成功的话）
        if formatted_uptime and download_success:
            mark_task_completed("1h", data_type, formatted_uptime)
            result_msg = f"处理完成: {formatted_uptime}"
        elif formatted_uptime and not download_success:
            # 有uptime但下载失败，仍然标记完成但显示失败信息
            mark_task_completed("1h", data_type, formatted_uptime)
            result_msg = f"处理完成（下载失败）: {formatted_uptime}"
        else:
            result_msg = "处理完成（下载失败）"

        logger.info(f"1小时任务 {data_type} 执行完成，{result_msg}")
        return result_msg

    async def _execute_hourly_final_steps(self, data_type: str, download_success: bool, formatted_uptime: str = None):
        """执行1小时任务的最终步骤，无论下载是否成功都要执行"""
        logger.info(f"开始执行1小时任务 {data_type} 的最终步骤...")

        try:
            # 对于PRE数据类型，需要调用汇总存储过程
            if data_type == 'PRE':
                logger.info("开始调用PRE数据汇总存储过程...")

                # 导入数据库管理器和汇总函数
                from weather_cell_1h import AsyncDatabaseManager, call_summary_procedures_async
                from config import PG_URL

                db_manager = AsyncDatabaseManager(PG_URL)
                await db_manager.initialize()
                try:
                    await call_summary_procedures_async(db_manager)
                    logger.info("✓ PRE数据汇总存储过程完成")
                except Exception as e:
                    logger.error(f"PRE数据汇总存储过程失败: {e}")
                finally:
                    await db_manager.close()
            else:
                logger.info(f"数据类型 {data_type} 无需执行特殊的最终步骤")

        except Exception as e:
            logger.error(f"执行1小时任务 {data_type} 最终步骤时发生错误: {e}")
            # 不抛出异常，避免影响任务完成标记

    async def _execute_six_minute_final_steps(self, download_success: bool, formatted_uptime: str = None):
        """执行6分钟任务的最终步骤，无论下载是否成功都要执行"""
        logger.info("开始执行6分钟任务的最终步骤...")

        try:
            # 6分钟任务暂时没有必须执行的最终步骤，保留空函数
            logger.info("6分钟任务暂时无需执行特殊的最终步骤")
            pass

        except Exception as e:
            logger.error(f"执行6分钟任务最终步骤时发生错误: {e}")
            # 不抛出异常，避免影响任务完成标记

    async def _execute_observation_task(self) -> Any:
        """执行实况数据任务"""
        logger.info("开始执行实况数据任务")

        download_success = False
        formatted_uptime = None

        try:
            # 1. 获取当前任务状态，用于检查uptime新鲜度
            from task_state_manager import task_state_manager
            task_state = task_state_manager.get_task_state("obs", self.observation_data_type)
            last_processed_uptime = task_state.last_uptime if task_state else None

            # 2. 检查数据新鲜度并获取下载信息（会自动重试直到获取到新数据）
            api_result = await check_and_get_fresh_data(self.observation_data_type, "obs", last_processed_uptime)

            if not api_result:
                raise Exception("无法获取新鲜的实况数据")

            # 3. 获取uptime信息
            current_uptime = api_result.get('uptime', '')
            if not current_uptime:
                raise Exception("API响应中未找到uptime信息")

            # 格式化uptime（确保是YYYYMMDDHHMM格式）
            formatted_uptime = self._format_uptime(current_uptime)
            if not formatted_uptime:
                raise Exception(f"无效的uptime格式: {current_uptime}")

            logger.info(f"获取到新实况数据，uptime: {formatted_uptime}，开始下载...")

            # 3. 立即标记任务开始处理（更新uptime状态）
            mark_task_started("obs", self.observation_data_type, formatted_uptime)

            # 4. 异步下载数据（使用线程池避免阻塞）
            loop = asyncio.get_event_loop()
            downloaded_files = await loop.run_in_executor(
                self.resource_manager.get_thread_pool(),
                download_weather_data_with_api_result,
                self.observation_data_type, api_result
            )
            if not downloaded_files:
                raise Exception("下载实况数据失败")

            logger.info(f"实况数据下载完成: {downloaded_files}")
            download_success = True

            # 5. 执行数据处理
            logger.info("开始处理实况数据...")

            # 调用实际的数据处理逻辑（使用线程池异步执行）
            try:
                from weather_cell_obs import process_obs_weather_data_hybrid

                # 使用线程池异步执行数据处理，避免阻塞其他任务
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    self.resource_manager.get_thread_pool(),
                    lambda: asyncio.run(process_obs_weather_data_hybrid(downloaded_files))
                )
                logger.info("实况数据处理完成")

            except Exception as e:
                logger.error(f"实况数据处理失败: {e}")
                # 不抛出异常，让任务标记为完成但记录错误

        except Exception as e:
            logger.error(f"执行实况数据任务失败: {e}")
            raise

        finally:
            # 6. 执行最终步骤（无论成功失败都要执行）
            await self._execute_observation_final_steps(download_success, formatted_uptime)

            # 7. 标记任务完成
            if formatted_uptime:
                mark_task_completed("obs", self.observation_data_type, formatted_uptime)
                logger.info(f"实况数据任务完成，uptime: {formatted_uptime}")
            else:
                logger.warning("实况数据任务完成，但未获取到有效的uptime")

        return download_success

    async def _execute_observation_final_steps(self, download_success: bool, formatted_uptime: str = None):
        """执行实况数据任务的最终步骤，无论下载是否成功都要执行"""
        logger.info("开始执行实况数据任务的最终步骤...")

        try:
            # 实况数据任务暂时没有必须执行的最终步骤，保留空函数
            pass

        except Exception as e:
            logger.error(f"执行实况数据任务最终步骤时发生错误: {e}")
            # 不抛出异常，避免影响任务完成标记

    async def _execute_six_minute_task(self) -> Any:
        """执行6分钟任务"""
        logger.info("开始执行6分钟任务")

        download_success = False
        formatted_uptime = None

        try:
            # 1. 获取当前任务状态，用于检查uptime新鲜度
            from task_state_manager import task_state_manager
            task_state = task_state_manager.get_task_state("6m", self.six_minute_data_type)
            last_processed_uptime = task_state.last_uptime if task_state else None

            # 2. 检查数据新鲜度并获取下载信息（会自动重试直到获取到新数据）
            api_result = await check_and_get_fresh_data(self.six_minute_data_type, "6m", last_processed_uptime)

            if not api_result:
                raise Exception("无法获取新鲜的6分钟数据")

            # 3. 获取uptime信息
            current_uptime = api_result.get('uptime', '')
            if not current_uptime:
                raise Exception("API响应中未找到uptime信息")

            # 格式化uptime（确保是YYYYMMDDHHMM格式）
            formatted_uptime = self._format_uptime(current_uptime)
            if not formatted_uptime:
                raise Exception(f"无效的uptime格式: {current_uptime}")

            logger.info(f"获取到新数据，uptime: {formatted_uptime}，开始下载...")

            # 3. 立即标记任务开始处理（更新uptime状态）
            mark_task_started("6m", self.six_minute_data_type, formatted_uptime)

            # 4. 异步下载数据（使用线程池避免阻塞）
            loop = asyncio.get_event_loop()
            downloaded_files = await loop.run_in_executor(
                self.resource_manager.get_thread_pool(),
                download_weather_data_with_api_result,
                self.six_minute_data_type, api_result
            )
            if not downloaded_files:
                raise Exception("下载6分钟数据失败")

            logger.info(f"数据下载完成: {downloaded_files}")
            download_success = True

            # 5. 执行数据处理
            logger.info("开始处理6分钟数据...")

            # 调用实际的数据处理逻辑（使用线程池异步执行）
            try:
                from weather_cell_6m import process_weather_data_hybrid

                # 6分钟数据通常只有一个文件，取第一个
                downloaded_file = downloaded_files[0] if downloaded_files else None
                if not downloaded_file:
                    raise Exception("下载的文件列表为空")

                # 使用线程池异步执行数据处理，避免阻塞其他任务
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    self.resource_manager.get_thread_pool(),
                    lambda: asyncio.run(process_weather_data_hybrid(self.six_minute_data_type, downloaded_file))
                )
                logger.info("6分钟数据处理完成")

            except Exception as e:
                logger.error(f"6分钟数据处理失败: {e}")
                # 不抛出异常，让任务标记为完成但记录错误

        except Exception as e:
            logger.error(f"6分钟任务下载失败: {e}")
            download_success = False

        # 6. 无论下载是否成功，都要执行最终步骤
        try:
            await self._execute_six_minute_final_steps(download_success, formatted_uptime)
        except Exception as e:
            logger.error(f"6分钟任务最终步骤执行失败: {e}")

        # 7. 标记任务完成（如果有uptime且下载成功的话）
        if formatted_uptime and download_success:
            mark_task_completed("6m", self.six_minute_data_type, formatted_uptime)
            result_msg = f"处理完成: {formatted_uptime}"
        elif formatted_uptime and not download_success:
            # 有uptime但下载失败，仍然标记完成但显示失败信息
            mark_task_completed("6m", self.six_minute_data_type, formatted_uptime)
            result_msg = f"处理完成（下载失败）: {formatted_uptime}"
        else:
            result_msg = "处理完成（下载失败）"

        logger.info(f"6分钟任务执行完成，{result_msg}")
        return result_msg

    async def _execute_alarm_task(self):
        """执行预警任务"""
        logger.info("开始执行预警任务...")

        try:
            # 使用线程池异步执行预警任务，避免阻塞其他任务
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.resource_manager.get_thread_pool(),
                self._run_alarm_task_sync
            )
            logger.info("预警任务执行完成")

        except Exception as e:
            logger.error(f"执行预警任务失败: {e}")
            # 不抛出异常，让任务标记为完成但记录错误

    def _run_alarm_task_sync(self):
        """同步执行预警任务"""
        try:
            from weather_alarm import WeatherAlarmProcessor

            # 创建新的事件循环来运行异步代码
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                processor = WeatherAlarmProcessor()
                loop.run_until_complete(processor.initialize())
                loop.run_until_complete(processor.run_once())
                loop.run_until_complete(processor.close())
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"预警任务同步执行失败: {e}")
            raise

    async def _schedule_hourly_tasks(self):
        """调度1小时任务"""
        logger.info("启动1小时任务调度器...")
        
        while not self._shutdown:
            try:
                current_time = datetime.now()
                next_schedule = self._get_next_hourly_schedule(current_time)
                
                # 计算等待时间
                wait_seconds = (next_schedule - current_time).total_seconds()
                logger.info(f"下一个1小时任务调度时间: {next_schedule}, 等待 {wait_seconds:.1f} 秒")
                
                # 等待到调度时间，分段等待以便快速响应关闭信号
                if wait_seconds > 0:
                    await self._interruptible_sleep(wait_seconds)
                
                if self._shutdown:
                    break
                
                # 提交所有1小时任务
                for data_type in self.hourly_data_types:
                    task_func = lambda dt=data_type: self._execute_hourly_task(dt)
                    await self.task_manager.submit_task(
                        task_type="1h",
                        data_type=data_type,
                        scheduled_time=next_schedule,
                        task_func=task_func
                    )
                
                logger.info(f"已提交 {len(self.hourly_data_types)} 个1小时任务")
                
            except Exception as e:
                logger.error(f"调度1小时任务时发生错误: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试
    
    async def _schedule_six_minute_tasks(self):
        """调度6分钟任务"""
        logger.info("启动6分钟任务调度器...")
        
        while not self._shutdown:
            try:
                current_time = datetime.now()
                next_schedule = self._get_next_six_minute_schedule(current_time)
                
                # 计算等待时间
                wait_seconds = (next_schedule - current_time).total_seconds()
                logger.info(f"下一个6分钟任务调度时间: {next_schedule}, 等待 {wait_seconds:.1f} 秒")
                
                # 等待到调度时间，分段等待以便快速响应关闭信号
                if wait_seconds > 0:
                    await self._interruptible_sleep(wait_seconds)
                
                if self._shutdown:
                    break
                
                # 提交6分钟任务
                await self.task_manager.submit_task(
                    task_type="6m",
                    data_type=self.six_minute_data_type,
                    scheduled_time=next_schedule,
                    task_func=self._execute_six_minute_task
                )
                
                logger.info("已提交6分钟任务")
                
            except Exception as e:
                logger.error(f"调度6分钟任务时发生错误: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试

    async def _schedule_alarm_tasks(self):
        """调度预警任务"""
        logger.info("启动预警任务调度器...")

        while not self._shutdown:
            try:
                current_time = datetime.now()
                next_schedule = self._get_next_alarm_schedule(current_time)

                # 计算等待时间
                wait_seconds = (next_schedule - current_time).total_seconds()
                logger.info(f"下一个预警任务调度时间: {next_schedule}, 等待 {wait_seconds:.1f} 秒")

                # 等待到调度时间，分段等待以便快速响应关闭信号
                if wait_seconds > 0:
                    await self._interruptible_sleep(wait_seconds)

                if self._shutdown:
                    break

                # 提交预警任务
                await self.task_manager.submit_task(
                    task_type="alarm",
                    data_type="weather_alarm",
                    scheduled_time=next_schedule,
                    task_func=self._execute_alarm_task
                )

                logger.info("已提交预警任务")

            except Exception as e:
                logger.error(f"调度预警任务时发生错误: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试

    async def _schedule_observation_tasks(self):
        """调度实况数据任务"""
        logger.info("启动实况数据任务调度器...")

        while not self._shutdown:
            try:
                current_time = datetime.now()
                next_schedule = self._get_next_observation_schedule(current_time)

                # 计算等待时间
                wait_seconds = (next_schedule - current_time).total_seconds()
                logger.info(f"下一个实况数据任务调度时间: {next_schedule}, 等待 {wait_seconds:.1f} 秒")

                # 等待到调度时间，分段等待以便快速响应关闭信号
                if wait_seconds > 0:
                    await self._interruptible_sleep(wait_seconds)

                if self._shutdown:
                    break

                # 提交实况数据任务
                await self.task_manager.submit_task(
                    task_type="obs",
                    data_type=self.observation_data_type,
                    scheduled_time=next_schedule,
                    task_func=self._execute_observation_task
                )

                logger.info("已提交实况数据任务")

            except Exception as e:
                logger.error(f"调度实况数据任务时发生错误: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试

    async def _execute_initial_tasks(self):
        """启动时执行一次所有任务"""
        logger.info("启动时执行初始任务...")

        current_time = datetime.now()

        # 提交所有1小时任务
        for data_type in self.hourly_data_types:
            task_func = lambda dt=data_type: self._execute_hourly_task(dt)
            await self.task_manager.submit_task(
                task_type="1h",
                data_type=data_type,
                scheduled_time=current_time,
                task_func=task_func
            )
            logger.info(f"已提交初始1小时任务: {data_type}")

        # 提交6分钟任务
        await self.task_manager.submit_task(
            task_type="6m",
            data_type=self.six_minute_data_type,
            scheduled_time=current_time,
            task_func=self._execute_six_minute_task
        )
        logger.info("已提交初始6分钟任务")

        # 提交实况数据任务
        await self.task_manager.submit_task(
            task_type="obs",
            data_type=self.observation_data_type,
            scheduled_time=current_time,
            task_func=self._execute_observation_task
        )
        logger.info("已提交初始实况数据任务")

        # 提交预警任务
        await self.task_manager.submit_task(
            task_type="alarm",
            data_type="weather_alarm",
            scheduled_time=current_time,
            task_func=self._execute_alarm_task
        )
        logger.info("已提交初始预警任务")

        logger.info(f"已提交 {len(self.hourly_data_types) + 3} 个初始任务，将并发执行")

    async def start(self):
        """启动调度器"""
        logger.info("启动天气任务调度器...")

        # 初始化资源管理器
        await self.resource_manager.initialize()

        # 启动时先同步执行create_forecast_tables.py（必须完成才能继续）
        self._create_forecast_tables()

        # 启动任务管理器
        await self.task_manager.start_processing()

        # 取消启动时的初始任务执行，只按时间定时执行
        # await self._execute_initial_tasks()  # 已注释掉
        logger.info("跳过启动时的初始任务执行，将只按时间定时执行任务")

        # 启动调度任务
        self._scheduler_tasks = [
            asyncio.create_task(self._schedule_hourly_tasks()),
            asyncio.create_task(self._schedule_six_minute_tasks()),
            asyncio.create_task(self._schedule_observation_tasks()),
            asyncio.create_task(self._schedule_alarm_tasks())
        ]

        logger.info("天气任务调度器启动完成")
    
    async def stop(self):
        """停止调度器"""
        logger.info("停止天气任务调度器...")

        self._shutdown = True

        # 取消调度任务
        for task in self._scheduler_tasks:
            task.cancel()

        # 快速等待调度任务结束，设置超时
        if self._scheduler_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self._scheduler_tasks, return_exceptions=True),
                    timeout=2.0  # 最多等待2秒
                )
            except asyncio.TimeoutError:
                logger.warning("调度任务停止超时，强制继续")

        # 停止任务管理器
        try:
            await asyncio.wait_for(
                self.task_manager.stop_processing(),
                timeout=3.0  # 最多等待3秒
            )
        except asyncio.TimeoutError:
            logger.warning("任务管理器停止超时，强制继续")

        # 关闭资源管理器
        try:
            await asyncio.wait_for(
                self.resource_manager.close(),
                timeout=2.0  # 最多等待2秒
            )
        except asyncio.TimeoutError:
            logger.warning("资源管理器关闭超时，强制继续")

        logger.info("天气任务调度器已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'scheduler_running': not self._shutdown,
            'forecast_tables_created': self._forecast_tables_created,
            'next_hourly_schedule': self._get_next_hourly_schedule().isoformat(),
            'next_six_minute_schedule': self._get_next_six_minute_schedule().isoformat(),
            'next_alarm_schedule': self._get_next_alarm_schedule().isoformat(),
            'running_tasks': self.task_manager.get_running_tasks(),
            'queue_stats': self.task_manager.get_queue_stats(),
            'resource_stats': self.resource_manager.get_stats(),
            'task_states': task_state_manager.get_states_summary(),
            'task_statistics': task_state_manager.get_statistics()
        }


# 全局调度器实例
weather_scheduler = WeatherTaskScheduler()
